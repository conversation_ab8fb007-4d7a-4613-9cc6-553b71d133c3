package com.ejuetc.consumer.api.community;


import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.web.vo.CommunityTipsVO;
import com.ejuetc.consumer.web.vo.CommunityVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Tag(name = "API_小区")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "communityAPI4Consumer")
public interface CommunityAPI {

    @Operation(summary = "小区提示词查询")
    @PostMapping("/api/community/queryTips")
    ApiResponse<List<CommunityTipsVO>> queryCommunityNames(@RequestBody QueryTipsPO po);

    @PostMapping("/api/community/queryHasDetailByName")
    ApiResponse<List<CommunityVO>> queryHasDetailByName(@RequestBody QueryCommunityPO queryCommunityPO);

    @Operation(summary = "查询小区分组")
    @PostMapping("/api/community/queryLayoutGroup")
    ApiResponse<Map<Integer, Set<String>>> queryLayoutGroup(@RequestBody BindCommunityPO po);

    @Operation(summary = "查询小区详情")
    @PostMapping("/api/community/queryDetail")
    ApiResponse<CommunityVO> queryDetail(@RequestBody BindCommunityPO po);

    @Operation(summary = "绑定小区")
    @PostMapping("/api/community/bind")
    ApiResponse<BindCommunityRO> bind(
            @Parameter(description = "小区地址") @RequestParam("address") String address,
            @Parameter(description = "小区名称") @RequestParam("name") String name
    );

    @Operation(summary = "绑定小区详情")
    @PostMapping("/api/community/bindCommunityDetail")
    ApiResponse<?> bindCommunityDetail(@RequestBody List<String> cityCodes, @RequestParam int limitCount, @RequestParam int threadCount);

    @Operation(summary = "更新小区名文字重叠率")
    @PostMapping("/api/community/updateOverlapRate")
    ApiResponse<?> updateOverlapRate(@RequestParam int limitCount);

    @Operation(summary = "重新绑定小区为空的绑定信息")
    @PostMapping("/api/community/rebind")
    ApiResponse<?> rebind(@RequestParam int maxRowCount, @RequestParam int maxThreadCount);

    @Operation(summary = "上传小区图片到阿里云")
    @PostMapping("/api/community/convertCommunityPictureUrl")
    void convertCommunityPictureUrl(@RequestParam int maxRowCount, @RequestParam int batchSize);

    @Operation(summary = "上传小区户型图到阿里云")
    @PostMapping("/api/community/convertCommunityLayoutUrl")
    void convertCommunityLayoutUrl(@RequestParam int maxRowCount, @RequestParam int batchSize);
}
